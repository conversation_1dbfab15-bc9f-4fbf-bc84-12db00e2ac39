{"_format": "hh-sol-artifact-1", "contractName": "PaymentStream", "sourceName": "contracts/streams/PaymentStream.sol", "abi": [{"inputs": [{"internalType": "address", "name": "_feeCollector", "type": "address"}, {"internalType": "uint16", "name": "_feeInBasisPoints", "type": "uint16"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "InvalidAmount", "type": "error"}, {"inputs": [], "name": "InvalidDuration", "type": "error"}, {"inputs": [], "name": "InvalidFee", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientBalance", "type": "error"}, {"inputs": [], "name": "InvalidMilestonePercentage", "type": "error"}, {"inputs": [], "name": "InvalidRecipient", "type": "error"}, {"inputs": [], "name": "InvalidTokenAddress", "type": "error"}, {"inputs": [], "name": "MilestoneAlreadyReleased", "type": "error"}, {"inputs": [], "name": "MilestoneIndexOutOfBounds", "type": "error"}, {"inputs": [], "name": "NotStreamRecipient", "type": "error"}, {"inputs": [], "name": "NotStreamSender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "SafeERC20FailedOperation", "type": "error"}, {"inputs": [], "name": "StreamAlreadyActive", "type": "error"}, {"inputs": [], "name": "StreamAlreadyCanceled", "type": "error"}, {"inputs": [], "name": "StreamAlreadyCompleted", "type": "error"}, {"inputs": [], "name": "StreamAlreadyPaused", "type": "error"}, {"inputs": [], "name": "StreamNotActive", "type": "error"}, {"inputs": [], "name": "StreamNotFound", "type": "error"}, {"inputs": [], "name": "TokenNotSupported", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "streamId", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "milestoneIndex", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "MilestoneReleased", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "streamId", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "refundAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "StreamCanceled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "streamId", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "StreamCompleted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "streamId", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": true, "internalType": "address", "name": "recipient", "type": "address"}, {"indexed": false, "internalType": "address", "name": "tokenAddress", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "grossAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "startTime", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "endTime", "type": "uint256"}], "name": "StreamCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "streamId", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "StreamPaused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "streamId", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "StreamResumed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "streamId", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "streamed", "type": "uint256"}, {"indexed": false, "internalType": "enum PaymentStream.StreamStatus", "name": "status", "type": "uint8"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "StreamUpdated", "type": "event"}, {"inputs": [], "name": "MAX_FEE", "outputs": [{"internalType": "uint16", "name": "", "type": "uint16"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "streamId", "type": "bytes32"}], "name": "cancelStream", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "duration", "type": "uint256"}, {"internalType": "uint256[]", "name": "milestonePercentages", "type": "uint256[]"}, {"internalType": "string[]", "name": "milestoneDescriptions", "type": "string[]"}], "name": "createStream", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "feeCollector", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "feeInBasisPoints", "outputs": [{"internalType": "uint16", "name": "", "type": "uint16"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "streamId", "type": "bytes32"}, {"internalType": "uint256", "name": "milestoneIndex", "type": "uint256"}], "name": "getMilestone", "outputs": [{"internalType": "uint256", "name": "percentage", "type": "uint256"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "bool", "name": "released", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "streamId", "type": "bytes32"}], "name": "getMilestoneCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "streamId", "type": "bytes32"}], "name": "getStream", "outputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "streamed", "type": "uint256"}, {"internalType": "uint256", "name": "startTime", "type": "uint256"}, {"internalType": "uint256", "name": "endTime", "type": "uint256"}, {"internalType": "uint8", "name": "status", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "streamId", "type": "bytes32"}], "name": "getStreamedAmount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "streamId", "type": "bytes32"}], "name": "pauseStream", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "streamId", "type": "bytes32"}, {"internalType": "uint256", "name": "milestoneIndex", "type": "uint256"}], "name": "releaseMilestone", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "streamId", "type": "bytes32"}], "name": "resumeStream", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_feeCollector", "type": "address"}], "name": "setFeeCollector", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint16", "name": "_feeInBasisPoints", "type": "uint16"}], "name": "setFeeInBasisPoints", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "bool", "name": "isSupported", "type": "bool"}], "name": "setTokenSupport", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "name": "streams", "outputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "grossAmount", "type": "uint256"}, {"internalType": "uint256", "name": "streamed", "type": "uint256"}, {"internalType": "uint256", "name": "startTime", "type": "uint256"}, {"internalType": "uint256", "name": "endTime", "type": "uint256"}, {"internalType": "uint256", "name": "lastUpdate", "type": "uint256"}, {"internalType": "enum PaymentStream.StreamStatus", "name": "status", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "supportedTokens", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "streamId", "type": "bytes32"}], "name": "updateStream", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "streamId", "type": "bytes32"}], "name": "withdrawFromStream", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}